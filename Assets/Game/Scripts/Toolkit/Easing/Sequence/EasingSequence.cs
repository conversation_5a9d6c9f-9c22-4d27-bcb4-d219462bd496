using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Events;

namespace UncleChenGames.Easing
{
    /// <summary>
    /// 缓动序列 - 管理多个缓动动画的有序执行
    /// </summary>
    public class EasingSequence : EmptyCoroutine, IDisposable
    {
        private SequenceTimeline timeline;          // 时间轴管理
        private SequenceController controller;      // 播放控制
        private bool autoPlay = true;               // 是否自动播放
        private bool isReturningToPool = false;     // 是否正在返回对象池

        /// <summary>
        /// 获取序列总时长
        /// </summary>
        public override float Duration => timeline?.TotalDuration ?? 0f;

        /// <summary>
        /// 获取当前播放进度（0-1）
        /// </summary>
        public float Progress => Duration > 0 ? controller.ElapsedTime / Duration : 0f;

        /// <summary>
        /// 获取已完成的循环次数
        /// </summary>
        public int CompletedLoops => controller.CompletedLoops;

        /// <summary>
        /// 是否正在播放
        /// </summary>
        public bool IsPlaying => controller.State == SequenceController.PlaybackState.Playing;

        /// <summary>
        /// 是否已暂停
        /// </summary>
        public bool IsPaused => controller.State == SequenceController.PlaybackState.Paused;

        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsComplete => controller.State == SequenceController.PlaybackState.Completed;

        /// <summary>
        /// 构造函数
        /// </summary>
        public EasingSequence()
        {
            timeline = new SequenceTimeline();
            controller = new SequenceController();
        }

        #region 序列构建方法

        /// <summary>
        /// 添加一个缓动动画（串行）
        /// </summary>
        public EasingSequence Append(IEasingCoroutine tween)
        {
            if (tween == null) return this;

            var item = new SequenceItem
            {
                Type = SequenceItem.ItemType.Tween,
                Tween = tween,
                Duration = GetTweenDuration(tween)
            };

            timeline.AddSequential(item);
            CheckAutoPlay();
            return this;
        }

        /// <summary>
        /// 在指定时间插入一个缓动动画
        /// </summary>
        public EasingSequence Insert(float time, IEasingCoroutine tween)
        {
            if (tween == null) return this;

            var item = new SequenceItem
            {
                Type = SequenceItem.ItemType.Tween,
                Tween = tween,
                Duration = GetTweenDuration(tween)
            };

            timeline.InsertAt(time, item);
            CheckAutoPlay();
            return this;
        }

        /// <summary>
        /// 添加一个与前一个动画并行的缓动
        /// </summary>
        public EasingSequence Join(IEasingCoroutine tween)
        {
            if (tween == null) return this;

            var item = new SequenceItem
            {
                Type = SequenceItem.ItemType.Tween,
                Tween = tween,
                Duration = GetTweenDuration(tween)
            };

            timeline.AddParallel(item);
            CheckAutoPlay();
            return this;
        }

        /// <summary>
        /// 添加延迟
        /// </summary>
        public EasingSequence AppendInterval(float interval)
        {
            timeline.AddInterval(interval);
            CheckAutoPlay();
            return this;
        }

        /// <summary>
        /// 添加回调函数
        /// </summary>
        public EasingSequence AppendCallback(UnityAction callback)
        {
            if (callback == null) return this;

            var item = new SequenceItem
            {
                Type = SequenceItem.ItemType.Callback,
                Callback = callback,
                Duration = 0f
            };

            timeline.AddSequential(item);
            CheckAutoPlay();
            return this;
        }

        #endregion

        #region 控制方法

        /// <summary>
        /// 设置循环
        /// </summary>
        /// <param name="loops">循环次数，-1为无限循环</param>
        /// <param name="loopType">循环类型</param>
        public EasingSequence SetLoops(int loops, LoopType loopType = LoopType.Restart)
        {
            controller.SetLoops(loops, loopType);
            return this;
        }

        /// <summary>
        /// 设置时间缩放
        /// </summary>
        public EasingSequence SetTimeScale(float timeScale)
        {
            controller.SetTimeScale(timeScale);
            return this;
        }

        /// <summary>
        /// 设置是否自动播放
        /// </summary>
        public EasingSequence SetAutoPlay(bool auto)
        {
            autoPlay = auto;
            return this;
        }

        /// <summary>
        /// 播放序列
        /// </summary>
        public void Play()
        {
            if (IsActive) return;

            controller.Play();
            IsActive = true;
            
            // 注册到管理器
            EasingSequenceManager.RegisterSequence(this);
            
            coroutine = EasingManager.StartCustomCoroutine(PlayCoroutine());
        }

        /// <summary>
        /// 暂停序列
        /// </summary>
        public void Pause()
        {
            controller.Pause();

            // 暂停所有活跃的Tween
            var activeItems = timeline.GetActiveItems();
            foreach (var item in activeItems)
            {
                if (item.Type == SequenceItem.ItemType.Tween && item.Tween != null)
                {
                    item.IsPaused = true;
                    // 记录暂停时的进度
                    item.PausedTime = controller.ElapsedTime - item.StartTime;
                }
            }
        }

        /// <summary>
        /// 恢复播放
        /// </summary>
        public void Resume()
        {
            controller.Resume();
        }

        /// <summary>
        /// 重新开始
        /// </summary>
        public void Restart()
        {
            Kill(false);
            timeline.Reset();
            controller.Restart();
            IsActive = true;
            
            // 注册到管理器
            EasingSequenceManager.RegisterSequence(this);
            
            coroutine = EasingManager.StartCustomCoroutine(PlayCoroutine());
        }

        /// <summary>
        /// 停止序列
        /// </summary>
        /// <param name="complete">是否完成所有动画</param>
        public void Kill(bool complete = false)
        {
            // 如果已经不活跃，直接返回
            if (!IsActive && coroutine == null)
            {
                return;
            }

            if (complete)
            {
                // 完成所有未完成的项
                var allItems = timeline.Items;
                foreach (var item in allItems)
                {
                    if (!item.IsCompleted)
                    {
                        ExecuteItem(item, true);
                    }
                }

                // 触发完成回调
                finishCallback?.Invoke();
            }
            else
            {
                // 停止所有活跃的Tween
                var activeItems = timeline.GetActiveItems();
                foreach (var item in activeItems)
                {
                    if (item.Type == SequenceItem.ItemType.Tween && item.Tween != null)
                    {
                        item.Tween.Stop();
                    }
                }
            }

            // 停止控制器
            controller?.Stop();
            
            // 标记为非活跃
            IsActive = false;

            // 停止协程
            if (coroutine != null)
            {
                EasingManager.StopCustomCoroutine(coroutine);
                coroutine = null;
            }

            // 清理时间轴状态
            timeline?.Reset();
        }


        #endregion

        #region 私有方法

        /// <summary>
        /// 播放协程
        /// </summary>
        private IEnumerator PlayCoroutine()
        {
            while (controller.State != SequenceController.PlaybackState.Completed &&
                   controller.State != SequenceController.PlaybackState.Stopped)
            {
                yield return null;

                if (controller.State == SequenceController.PlaybackState.Playing)
                {
                    float deltaTime = unscaledTime ? Time.unscaledDeltaTime : Time.deltaTime;
                    float currentTime = controller.UpdateTime(deltaTime, timeline.TotalDuration);
                    UpdateSequence(currentTime);
                }
            }

            // 序列完成
            finishCallback?.Invoke();
            IsActive = false;
            
            // 自动返回对象池
            ReturnToPool();
        }

        /// <summary>
        /// 更新序列状态
        /// </summary>
        private void UpdateSequence(float currentTime)
        {
            // 获取需要开始的项
            var itemsToStart = timeline.GetItemsToExecuteAt(currentTime);
            foreach (var item in itemsToStart)
            {
                StartItem(item, currentTime);
            }

            // 获取需要完成的项
            var itemsToComplete = timeline.GetItemsToCompleteAt(currentTime);
            foreach (var item in itemsToComplete)
            {
                CompleteItem(item);
            }
        }

        /// <summary>
        /// 返回对象池
        /// </summary>
        private void ReturnToPool()
        {
            if (!isReturningToPool)
            {
                isReturningToPool = true;
                
                // 从管理器注销
                EasingSequenceManager.UnregisterSequence(this);
                
                SequenceExtensions.ReturnToPool(this);
            }
        }

        /// <summary>
        /// 开始执行序列项
        /// </summary>
        private void StartItem(SequenceItem item, float currentTime)
        {
            if (item.IsStarted) return;

            item.IsStarted = true;

            switch (item.Type)
            {
                case SequenceItem.ItemType.Tween:
                    if (item.Tween != null)
                    {
                        // 设置Tween的属性
                        item.Tween.SetUnscaledTime(unscaledTime);
                        if (delay > 0) item.Tween.SetDelay(delay);
                        if (useCurve) item.Tween.SetEasingCurve(easingCurve);
                        else item.Tween.SetEasing(easingType);
                    }
                    break;

                case SequenceItem.ItemType.Callback:
                    item.Callback?.Invoke();
                    item.IsCompleted = true;
                    break;
            }
        }

        /// <summary>
        /// 完成序列项
        /// </summary>
        private void CompleteItem(SequenceItem item)
        {
            if (item.IsCompleted) return;

            item.IsCompleted = true;

            if (item.Type == SequenceItem.ItemType.Tween && item.Tween != null)
            {
                // Tween会自行完成，这里只标记状态
            }
        }

        /// <summary>
        /// 执行序列项（用于立即完成）
        /// </summary>
        private void ExecuteItem(SequenceItem item, bool complete)
        {
            switch (item.Type)
            {
                case SequenceItem.ItemType.Tween:
                    if (item.Tween != null && complete)
                    {
                        // 立即完成Tween
                        item.Tween.Stop();
                    }
                    break;

                case SequenceItem.ItemType.Callback:
                    if (!item.IsStarted)
                    {
                        item.Callback?.Invoke();
                    }
                    break;
            }

            item.IsStarted = true;
            item.IsCompleted = true;
        }

        /// <summary>
        /// 获取Tween的持续时间
        /// </summary>
        private float GetTweenDuration(IEasingCoroutine tween)
        {
            return tween?.Duration ?? 0f;
        }

        /// <summary>
        /// 检查是否自动播放
        /// </summary>
        private void CheckAutoPlay()
        {
            if (autoPlay && !IsActive && timeline.Items.Count > 0)
            {
                Play();
            }
        }

        #endregion

        #region 资源管理

        /// <summary>
        /// 重置序列到初始状态（用于对象池复用）
        /// </summary>
        public void Reset()
        {
            // 停止当前播放
            if (IsActive)
            {
                Kill(false);
            }

            // 重置时间轴
            timeline?.Clear();
            
            // 重置控制器
            controller?.Restart();
            
            // 重置状态
            autoPlay = true;
            IsActive = false;
            isReturningToPool = false;
            coroutine = null;
            finishCallback = null;
            easingType = EasingType.Linear;
            delay = -1;
            unscaledTime = false;
            useCurve = false;
            easingCurve = null;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 停止并清理
            Kill(false);
            
            // 清理时间轴
            timeline?.Clear();
            timeline = null;
            
            // 清理控制器
            controller?.Stop();
            controller = null;
            
            // 清理回调
            finishCallback = null;
            
            // 清理协程
            if (coroutine != null)
            {
                EasingManager.StopCustomCoroutine(coroutine);
                coroutine = null;
            }
        }

        #endregion
    }
}