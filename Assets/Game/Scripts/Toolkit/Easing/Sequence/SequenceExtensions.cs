using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using UncleChenGames.Pool;

namespace UncleChenGames.Easing
{
    /// <summary>
    /// 序列扩展方法 - 为Unity组件提供便捷的序列动画功能
    /// </summary>
    public static class SequenceExtensions
    {
        // 序列对象池（初始容量50个）
        private static EasingSequencePool sequencePool;
        
        /// <summary>
        /// 获取序列对象池（延迟初始化）
        /// </summary>
        private static EasingSequencePool SequencePool
        {
            get
            {
                if (sequencePool == null)
                {
                    sequencePool = new EasingSequencePool("EasingSequence", 50);
                }
                return sequencePool;
            }
        }

        #region 序列创建

        /// <summary>
        /// 创建一个新的缓动序列
        /// </summary>
        public static EasingSequence DoSequence(this Transform transform)
        {
            return SequencePool.GetEntity();
        }

        /// <summary>
        /// 创建一个新的缓动序列
        /// </summary>
        public static EasingSequence DoSequence(this GameObject gameObject)
        {
            return SequencePool.GetEntity();
        }

        /// <summary>
        /// 创建一个新的缓动序列
        /// </summary>
        public static EasingSequence DoSequence(this Component component)
        {
            return SequencePool.GetEntity();
        }

        /// <summary>
        /// 返回序列到对象池
        /// </summary>
        public static void ReturnToPool(EasingSequence sequence)
        {
            if (sequence == null) return;
            
            // 调用对象池的返回方法
            SequencePool.ReturnEntity(sequence);
        }

        /// <summary>
        /// 清理对象池
        /// </summary>
        public static void ClearPool()
        {
            if (sequencePool != null)
            {
                sequencePool.Destroy();
                sequencePool = null;
            }
        }

        #endregion

        #region 预设动画效果

        /// <summary>
        /// 震动效果
        /// </summary>
        /// <param name="transform">目标Transform</param>
        /// <param name="duration">持续时间</param>
        /// <param name="strength">震动强度</param>
        /// <param name="vibrato">震动频率</param>
        /// <param name="randomness">随机度（0-1）</param>
        public static EasingSequence DoShake(this Transform transform, float duration,
            float strength = 1f, int vibrato = 10, float randomness = 90f)
        {
            var sequence = SequencePool.GetEntity();
            Vector3 originalPosition = transform.position;

            float elapsed = 0f;
            float shakeTime = duration / vibrato;

            for (int i = 0; i < vibrato; i++)
            {
                Vector3 randomOffset = new Vector3(
                    Random.Range(-strength, strength),
                    Random.Range(-strength, strength),
                    0
                );

                // 随着时间衰减震动强度
                float decay = 1f - (elapsed / duration);
                randomOffset *= decay;

                sequence.Append(transform.DoPosition(originalPosition + randomOffset, shakeTime)
                    .SetEasing(EasingType.CubicInOut));

                elapsed += shakeTime;
            }

            // 最后回到原始位置
            sequence.Append(transform.DoPosition(originalPosition, shakeTime * 0.5f)
                .SetEasing(EasingType.QuadOut));

            return sequence;
        }

        /// <summary>
        /// 冲击效果（弹性反弹）
        /// </summary>
        /// <param name="transform">目标Transform</param>
        /// <param name="punch">冲击向量</param>
        /// <param name="duration">持续时间</param>
        public static EasingSequence DoPunch(this Transform transform, Vector3 punch, float duration)
        {
            var sequence = SequencePool.GetEntity();
            Vector3 originalPosition = transform.position;

            // 快速移动到冲击位置
            sequence.Append(transform.DoPosition(originalPosition + punch, duration * 0.3f)
                .SetEasing(EasingType.QuadOut));

            // 弹性回弹
            sequence.Append(transform.DoPosition(originalPosition, duration * 0.7f)
                .SetEasing(EasingType.ElasticOut));

            return sequence;
        }

        /// <summary>
        /// 路径动画
        /// </summary>
        /// <param name="transform">目标Transform</param>
        /// <param name="waypoints">路径点数组</param>
        /// <param name="duration">总持续时间</param>
        /// <param name="pathType">路径类型（预留参数）</param>
        public static EasingSequence DoPath(this Transform transform, Vector3[] waypoints,
            float duration, EasingType easingType = EasingType.Linear)
        {
            var sequence = SequencePool.GetEntity();

            if (waypoints == null || waypoints.Length == 0) return sequence;

            float segmentDuration = duration / waypoints.Length;

            for (int i = 0; i < waypoints.Length; i++)
            {
                sequence.Append(transform.DoPosition(waypoints[i], segmentDuration)
                    .SetEasing(easingType));
            }

            return sequence;
        }

        /// <summary>
        /// 脉冲缩放效果
        /// </summary>
        /// <param name="transform">目标Transform</param>
        /// <param name="scale">脉冲缩放值</param>
        /// <param name="duration">持续时间</param>
        public static EasingSequence DoPulse(this Transform transform, float scale, float duration)
        {
            var sequence = SequencePool.GetEntity();
            Vector3 originalScale = transform.localScale;

            sequence.Append(transform.DoLocalScale(originalScale * scale, duration * 0.5f)
                .SetEasing(EasingType.QuadOut))
                .Append(transform.DoLocalScale(originalScale, duration * 0.5f)
                .SetEasing(EasingType.QuadIn));

            return sequence;
        }

        /// <summary>
        /// 闪烁效果（适用于UI或SpriteRenderer）
        /// </summary>
        /// <param name="graphic">UI图形组件</param>
        /// <param name="duration">总持续时间</param>
        /// <param name="interval">闪烁间隔</param>
        public static EasingSequence DoFlash(this Graphic graphic, float duration, float interval = 0.1f)
        {
            var sequence = SequencePool.GetEntity();
            int flashCount = Mathf.FloorToInt(duration / (interval * 2));

            for (int i = 0; i < flashCount; i++)
            {
                sequence.Append(graphic.DoAlpha(0, interval))
                    .Append(graphic.DoAlpha(1, interval));
            }

            return sequence;
        }

        /// <summary>
        /// 闪烁效果（SpriteRenderer版本）
        /// </summary>
        public static EasingSequence DoFlash(this SpriteRenderer sprite, float duration, float interval = 0.1f)
        {
            var sequence = SequencePool.GetEntity();
            int flashCount = Mathf.FloorToInt(duration / (interval * 2));

            for (int i = 0; i < flashCount; i++)
            {
                sequence.Append(sprite.DoAlpha(0, interval))
                    .Append(sprite.DoAlpha(1, interval));
            }

            return sequence;
        }

        #endregion

        #region UI动画预设

        /// <summary>
        /// UI弹出效果
        /// </summary>
        public static EasingSequence DoPopIn(this Transform transform, float duration = 0.3f)
        {
            var sequence = SequencePool.GetEntity();

            // 从0开始缩放
            transform.localScale = Vector3.zero;

            sequence.Append(transform.DoLocalScale(Vector3.one * 1.1f, duration * 0.7f)
                .SetEasing(EasingType.BackOut))
                .Append(transform.DoLocalScale(Vector3.one, duration * 0.3f)
                .SetEasing(EasingType.QuadOut));

            return sequence;
        }

        /// <summary>
        /// UI收起效果
        /// </summary>
        public static EasingSequence DoPopOut(this Transform transform, float duration = 0.3f)
        {
            var sequence = SequencePool.GetEntity();

            sequence.Append(transform.DoLocalScale(Vector3.one * 1.1f, duration * 0.3f)
                .SetEasing(EasingType.QuadOut))
                .Append(transform.DoLocalScale(Vector3.zero, duration * 0.7f)
                .SetEasing(EasingType.BackIn));

            return sequence;
        }

        /// <summary>
        /// 淡入效果
        /// </summary>
        public static EasingSequence DoFadeIn(this CanvasGroup canvasGroup, float duration = 0.3f)
        {
            var sequence = SequencePool.GetEntity();

            canvasGroup.alpha = 0;
            canvasGroup.gameObject.SetActive(true);

            sequence.Append(canvasGroup.DoAlpha(1, duration)
                .SetEasing(EasingType.QuadOut));

            return sequence;
        }

        /// <summary>
        /// 淡出效果
        /// </summary>
        public static EasingSequence DoFadeOut(this CanvasGroup canvasGroup, float duration = 0.3f)
        {
            var sequence = SequencePool.GetEntity();

            sequence.Append(canvasGroup.DoAlpha(0, duration)
                .SetEasing(EasingType.QuadIn))
                .AppendCallback(() => canvasGroup.gameObject.SetActive(false));

            return sequence;
        }

        #endregion

        #region 组合动画示例

        /// <summary>
        /// 收集物品效果（飞向目标位置）
        /// </summary>
        public static EasingSequence DoCollect(this Transform transform, Transform target,
            float duration = 1f, float height = 2f)
        {
            var sequence = SequencePool.GetEntity();
            Vector3 startPos = transform.position;
            Vector3 endPos = target.position;

            // 计算中间点（抛物线顶点）
            Vector3 midPoint = (startPos + endPos) * 0.5f;
            midPoint.y += height;

            // 先向上移动
            sequence.Append(transform.DoPosition(midPoint, duration * 0.4f)
                .SetEasing(EasingType.QuadOut));

            // 同时开始旋转和缩放
            sequence.Join(transform.DoLocalRotation(Quaternion.Euler(0, 360, 0), duration)
                .SetEasing(EasingType.Linear));

            // 然后快速飞向目标
            sequence.Append(transform.DoPosition(endPos, duration * 0.6f)
                .SetEasing(EasingType.QuadIn));

            // 在最后0.2秒缩小消失
            sequence.Insert(duration * 0.8f, transform.DoLocalScale(Vector3.zero, duration * 0.2f)
                .SetEasing(EasingType.QuadIn));

            return sequence;
        }

        /// <summary>
        /// 爆炸消散效果
        /// </summary>
        public static EasingSequence DoExplode(this Transform transform, float duration = 0.5f)
        {
            var sequence = SequencePool.GetEntity();

            // 先快速放大
            sequence.Append(transform.DoLocalScale(transform.localScale * 1.5f, duration * 0.3f)
                .SetEasing(EasingType.QuadOut));

            // 然后快速缩小并旋转
            sequence.Append(transform.DoLocalScale(Vector3.zero, duration * 0.7f)
                .SetEasing(EasingType.QuadIn));

            // 同时旋转
            sequence.Join(transform.DoLocalRotation(Quaternion.Euler(0, 0, 180), duration * 0.7f)
                .SetEasing(EasingType.QuadIn));

            return sequence;
        }

        #endregion
    }
}