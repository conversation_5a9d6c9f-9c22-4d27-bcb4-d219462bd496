using System.Collections.Generic;
using UnityEngine;

namespace UncleChenGames.Easing
{
    /// <summary>
    /// 缓动序列管理器 - 管理所有活跃的序列，处理生命周期
    /// </summary>
    public class EasingSequenceManager : MonoBehaviour
    {
        private static EasingSequenceManager instance;
        private HashSet<EasingSequence> activeSequences = new HashSet<EasingSequence>();

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static EasingSequenceManager Instance
        {
            get
            {
                if (instance == null)
                {
                    GameObject managerObject = new GameObject("EasingSequenceManager");
                    instance = managerObject.AddComponent<EasingSequenceManager>();
                }
                return instance;
            }
        }

        private void Awake()
        {
            // 确保只有一个实例
            if (instance != null && instance != this)
            {
                Destroy(gameObject);
                return;
            }

            instance = this;
            DontDestroyOnLoad(gameObject);
        }

        /// <summary>
        /// 注册活跃序列
        /// </summary>
        public static void RegisterSequence(EasingSequence sequence)
        {
            if (sequence != null)
            {
                Instance.activeSequences.Add(sequence);
            }
        }

        /// <summary>
        /// 注销序列
        /// </summary>
        public static void UnregisterSequence(EasingSequence sequence)
        {
            if (instance != null && sequence != null)
            {
                instance.activeSequences.Remove(sequence);
            }
        }

        /// <summary>
        /// 清理所有活跃序列
        /// </summary>
        public static void ClearAllSequences()
        {
            if (instance != null)
            {
                // 复制集合以避免在迭代时修改
                var sequencesToClear = new List<EasingSequence>(instance.activeSequences);

                foreach (var sequence in sequencesToClear)
                {
                    if (sequence != null && sequence.IsActive)
                    {
                        sequence.Kill(false);
                    }
                }

                instance.activeSequences.Clear();
            }
        }

        /// <summary>
        /// 清理对象池
        /// </summary>
        public static void ClearPool()
        {
            // 通过 SequenceExtensions 访问对象池
            // 由于对象池是私有的，这里需要添加一个公共方法
            SequenceExtensions.ClearPool();
        }

        private void OnDestroy()
        {
            // 场景销毁时清理所有序列
            ClearAllSequences();

            if (instance == this)
            {
                instance = null;
            }
        }

        /// <summary>
        /// 场景加载时的处理
        /// </summary>
        private void OnEnable()
        {
            UnityEngine.SceneManagement.SceneManager.sceneLoaded += OnSceneLoaded;
        }

        private void OnDisable()
        {
            UnityEngine.SceneManagement.SceneManager.sceneLoaded -= OnSceneLoaded;
        }

        private void OnSceneLoaded(UnityEngine.SceneManagement.Scene scene, UnityEngine.SceneManagement.LoadSceneMode mode)
        {
            // 场景切换时清理所有序列
            if (mode == UnityEngine.SceneManagement.LoadSceneMode.Single)
            {
                ClearAllSequences();
            }
        }

        /// <summary>
        /// 获取当前活跃序列数量
        /// </summary>
        public static int ActiveSequenceCount => instance != null ? instance.activeSequences.Count : 0;
    }
}