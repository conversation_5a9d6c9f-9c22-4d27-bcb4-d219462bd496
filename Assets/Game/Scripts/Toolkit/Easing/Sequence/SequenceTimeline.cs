using System.Collections.Generic;
using UnityEngine;

namespace UncleChenGames.Easing
{
    /// <summary>
    /// 序列时间轴管理器 - 管理所有序列项的时间轴
    /// </summary>
    internal class SequenceTimeline
    {
        private const int MAX_ITEMS = 1000;                            // 最大序列项数量限制
        private List<SequenceItem> items = new List<SequenceItem>();  // 所有序列项
        private float currentInsertTime = 0f;                         // 当前插入时间点
        private float totalDuration = 0f;                              // 总持续时间
        
        // 缓存列表，避免每次查询都创建新列表
        private List<SequenceItem> cachedExecuteItems = new List<SequenceItem>();
        private List<SequenceItem> cachedCompleteItems = new List<SequenceItem>();
        private List<SequenceItem> cachedActiveItems = new List<SequenceItem>();
        
        // 线程同步对象
        private readonly object syncLock = new object();

        /// <summary>
        /// 获取序列项列表
        /// </summary>
        public List<SequenceItem> Items => items;

        /// <summary>
        /// 获取总持续时间
        /// </summary>
        public float TotalDuration => totalDuration;

        /// <summary>
        /// 添加串行项（在当前时间轴末尾添加）
        /// </summary>
        public void AddSequential(SequenceItem item)
        {
            lock (syncLock)
            {
                // 检查容量限制
                if (items.Count >= MAX_ITEMS)
                {
                    Debug.LogWarning($"序列项数量已达到最大限制: {MAX_ITEMS}，无法添加新项");
                    return;
                }
                
                item.StartTime = currentInsertTime;
                items.Add(item);

                // 更新当前插入时间
                currentInsertTime = item.EndTime;

                // 更新总持续时间
                UpdateTotalDuration();
            }
        }

        /// <summary>
        /// 添加并行项（与前一个项并行）
        /// </summary>
        public void AddParallel(SequenceItem item)
        {
            if (items.Count == 0)
            {
                AddSequential(item);
                return;
            }

            // 检查容量限制
            if (items.Count >= MAX_ITEMS)
            {
                Debug.LogWarning($"序列项数量已达到最大限制: {MAX_ITEMS}，无法添加新项");
                return;
            }

            // 获取最后一个非并行项的开始时间
            float parallelStartTime = GetLastSequentialStartTime();
            item.StartTime = parallelStartTime;
            items.Add(item);

            // 如果这个并行项的结束时间超过了当前插入时间，需要更新
            if (item.EndTime > currentInsertTime)
            {
                currentInsertTime = item.EndTime;
            }

            // 更新总持续时间
            UpdateTotalDuration();
        }

        /// <summary>
        /// 在指定时间插入项
        /// </summary>
        public void InsertAt(float time, SequenceItem item)
        {
            // 检查容量限制
            if (items.Count >= MAX_ITEMS)
            {
                Debug.LogWarning($"序列项数量已达到最大限制: {MAX_ITEMS}，无法添加新项");
                return;
            }
            
            item.StartTime = time;
            items.Add(item);

            // 如果插入项的结束时间超过了当前插入时间，更新它
            if (item.EndTime > currentInsertTime)
            {
                currentInsertTime = item.EndTime;
            }

            // 更新总持续时间
            UpdateTotalDuration();
        }

        /// <summary>
        /// 添加延迟
        /// </summary>
        public void AddInterval(float interval)
        {
            currentInsertTime += interval;
            UpdateTotalDuration();
        }

        /// <summary>
        /// 获取在指定时间应该执行的项
        /// </summary>
        public List<SequenceItem> GetItemsToExecuteAt(float time)
        {
            // 清空缓存列表并重用，避免创建新列表
            cachedExecuteItems.Clear();
            
            // 使用for循环替代LINQ，提高性能
            for (int i = 0; i < items.Count; i++)
            {
                if (items[i].ShouldExecuteAt(time))
                {
                    cachedExecuteItems.Add(items[i]);
                }
            }
            
            return cachedExecuteItems;
        }

        /// <summary>
        /// 获取在指定时间应该完成的项
        /// </summary>
        public List<SequenceItem> GetItemsToCompleteAt(float time)
        {
            // 清空缓存列表并重用，避免创建新列表
            cachedCompleteItems.Clear();
            
            // 使用for循环替代LINQ，提高性能
            for (int i = 0; i < items.Count; i++)
            {
                if (items[i].ShouldCompleteAt(time))
                {
                    cachedCompleteItems.Add(items[i]);
                }
            }
            
            return cachedCompleteItems;
        }

        /// <summary>
        /// 获取所有活跃的项（已开始但未完成）
        /// </summary>
        public List<SequenceItem> GetActiveItems()
        {
            // 清空缓存列表并重用，避免创建新列表
            cachedActiveItems.Clear();
            
            // 使用for循环替代LINQ，提高性能
            for (int i = 0; i < items.Count; i++)
            {
                if (items[i].IsStarted && !items[i].IsCompleted)
                {
                    cachedActiveItems.Add(items[i]);
                }
            }
            
            return cachedActiveItems;
        }

        /// <summary>
        /// 重置时间轴
        /// </summary>
        public void Reset()
        {
            foreach (var item in items)
            {
                item.Reset();
            }
        }

        /// <summary>
        /// 清空时间轴
        /// </summary>
        public void Clear()
        {
            // 先停止所有活跃的Tween
            foreach (var item in items)
            {
                if (item.Type == SequenceItem.ItemType.Tween &&
                    item.Tween != null &&
                    item.Tween.IsActive)
                {
                    item.Tween.Stop();
                }
            }

            items.Clear();
            currentInsertTime = 0f;
            totalDuration = 0f;
        }

        /// <summary>
        /// 获取最后一个串行项的开始时间
        /// </summary>
        private float GetLastSequentialStartTime()
        {
            if (items.Count == 0) return 0f;

            // 从后往前找，找到第一个"串行"添加的项
            for (int i = items.Count - 1; i >= 0; i--)
            {
                var item = items[i];
                // 如果这个项的结束时间等于某个后续项的开始时间，说明它是串行的
                bool isSequential = false;

                for (int j = i + 1; j < items.Count; j++)
                {
                    if (Mathf.Approximately(item.EndTime, items[j].StartTime))
                    {
                        isSequential = true;
                        break;
                    }
                }

                if (isSequential || i == 0)
                {
                    return item.StartTime;
                }
            }

            return items[items.Count - 1].StartTime;
        }

        /// <summary>
        /// 更新总持续时间
        /// </summary>
        private void UpdateTotalDuration()
        {
            float maxEndTime = currentInsertTime;
            
            // 手动查找最大结束时间，避免使用LINQ
            for (int i = 0; i < items.Count; i++)
            {
                if (items[i].EndTime > maxEndTime)
                {
                    maxEndTime = items[i].EndTime;
                }
            }
            
            totalDuration = maxEndTime;
        }
    }
}