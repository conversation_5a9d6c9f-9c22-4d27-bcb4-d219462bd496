using UncleChenGames.Pool;
using UnityEngine;

namespace UncleChenGames.Easing
{
    /// <summary>
    /// 缓动序列对象池 - 管理EasingSequence对象的复用
    /// </summary>
    public class EasingSequencePool : Pool<EasingSequence>
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">对象池名称</param>
        /// <param name="startingSize">初始大小</param>
        public EasingSequencePool(string name, int startingSize) : base(name, startingSize)
        {
        }

        /// <summary>
        /// 创建新的缓动序列实例
        /// </summary>
        /// <returns>新的缓动序列实例</returns>
        protected override EasingSequence CreateEntity()
        {
            return new EasingSequence();
        }

        /// <summary>
        /// 初始化缓动序列实例
        /// </summary>
        /// <param name="entity">要初始化的实例</param>
        protected override void InitEntity(EasingSequence entity)
        {
            entity.Reset();
        }

        /// <summary>
        /// 验证缓动序列是否可用（未激活的才可用）
        /// </summary>
        /// <param name="entity">要验证的实例</param>
        /// <returns>是否可用</returns>
        protected override bool ValidateEntity(EasingSequence entity)
        {
            return !entity.IsActive;
        }

        /// <summary>
        /// 禁用缓动序列
        /// </summary>
        /// <param name="entity">要禁用的实例</param>
        protected override void DisableEntity(EasingSequence entity)
        {
            entity.Kill(false);
        }

        /// <summary>
        /// 销毁缓动序列
        /// </summary>
        /// <param name="entity">要销毁的实例</param>
        protected override void DestroyEntity(EasingSequence entity)
        {
            entity.Dispose();
        }

        /// <summary>
        /// 返回缓动序列到对象池
        /// </summary>
        /// <param name="entity">要返回的实例</param>
        public void ReturnEntity(EasingSequence entity)
        {
            if (entity == null || entity.IsActive)
            {
                return;
            }

            // 重置实体状态
            entity.Reset();
            
            // 这里可以调用基类的方法来管理实体的返回逻辑
            // 由于基类Pool<T>没有暴露返回方法，这里只是重置状态
            // 实际使用时，对象会在下次GetEntity时被复用
        }
    }
}